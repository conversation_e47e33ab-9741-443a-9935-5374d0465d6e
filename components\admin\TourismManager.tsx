"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { supabase } from "@/lib/supabase";
import { Plus, Edit2, Trash2, MapPin, Star, Clock } from "lucide-react";
import { ImageUpload } from "@/components/ui/image-upload";

interface Tourism {
  id: string;
  name: string;
  description: string;
  category: string;
  location: string;
  facilities: string | null;
  opening_hours: string | null;
  ticket_price: string | null;
  contact_info: string | null;
  image_url: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

export function TourismManager() {
  const [tourismList, setTourismList] = useState<Tourism[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingTourism, setEditingTourism] = useState<Tourism | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "Wisata Alam",
    location: "",
    facilities: "",
    opening_hours: "",
    ticket_price: "",
    contact_info: "",
    image_url: "",
    is_featured: false,
  });

  const categories = [
    "Wisata Alam",
    "Wisata Budaya",
    "Wisata Religi",
    "Wisata Kuliner",
    "Wisata Sejarah",
    "Wisata Edukasi",
    "Lainnya",
  ];

  useEffect(() => {
    fetchTourism();
  }, []);

  const fetchTourism = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("tourism")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setTourismList(data || []);
    } catch (error) {
      console.error("Error fetching tourism:", error);
      alert("Gagal memuat data wisata");
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      name: "",
      description: "",
      category: "Wisata Alam",
      location: "",
      facilities: "",
      opening_hours: "",
      ticket_price: "",
      contact_info: "",
      image_url: "",
      is_featured: false,
    });
    setEditingTourism(null);
  };

  const openDialog = (tourism?: Tourism) => {
    if (tourism) {
      setEditingTourism(tourism);
      setFormData({
        name: tourism.name,
        description: tourism.description,
        category: tourism.category,
        location: tourism.location,
        facilities: tourism.facilities || "",
        opening_hours: tourism.opening_hours || "",
        ticket_price: tourism.ticket_price || "",
        contact_info: tourism.contact_info || "",
        image_url: tourism.image_url,
        is_featured: tourism.is_featured,
      });
    } else {
      resetForm();
    }
    setDialogOpen(true);
  };

  const handleSave = async () => {
    if (
      !formData.name ||
      !formData.description ||
      !formData.location ||
      !formData.image_url
    ) {
      alert("Mohon lengkapi semua field yang wajib diisi");
      return;
    }

    setSaving(true);
    try {
      const dataToSave = {
        ...formData,
        facilities: formData.facilities || null,
        opening_hours: formData.opening_hours || null,
        ticket_price: formData.ticket_price || null,
        contact_info: formData.contact_info || null,
      };

      if (editingTourism) {
        // Update (excluding image_url as per requirement)
        const { error } = await supabase
          .from("tourism")
          .update({
            name: dataToSave.name,
            description: dataToSave.description,
            category: dataToSave.category,
            location: dataToSave.location,
            facilities: dataToSave.facilities,
            opening_hours: dataToSave.opening_hours,
            ticket_price: dataToSave.ticket_price,
            contact_info: dataToSave.contact_info,
            is_featured: dataToSave.is_featured,
            updated_at: new Date().toISOString(),
          })
          .eq("id", editingTourism.id);

        if (error) throw error;
      } else {
        // Create
        const { error } = await supabase.from("tourism").insert([dataToSave]);

        if (error) throw error;
      }

      await fetchTourism();
      setDialogOpen(false);
      resetForm();
      alert("Data wisata berhasil disimpan!");
    } catch (error) {
      console.error("Error saving tourism:", error);
      alert("Gagal menyimpan data wisata");
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!confirm("Apakah Anda yakin ingin menghapus data wisata ini?")) {
      return;
    }

    try {
      const { error } = await supabase.from("tourism").delete().eq("id", id);

      if (error) throw error;

      await fetchTourism();
      alert("Data wisata berhasil dihapus!");
    } catch (error) {
      console.error("Error deleting tourism:", error);
      alert("Gagal menghapus data wisata");
    }
  };

  const handleImageUploaded = (imageUrl: string) => {
    setFormData({ ...formData, image_url: imageUrl });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Kelola Wisata</h2>
        <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
          <DialogTrigger asChild>
            <Button onClick={() => openDialog()}>
              <Plus className="w-4 h-4 mr-2" />
              Tambah Wisata
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-3xl max-h-screen overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingTourism ? "Edit Wisata" : "Tambah Wisata"}
              </DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Nama Wisata *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) =>
                      setFormData({ ...formData, name: e.target.value })
                    }
                    placeholder="Nama tempat wisata"
                  />
                </div>
                <div>
                  <Label htmlFor="category">Kategori</Label>
                  <Select
                    value={formData.category}
                    onValueChange={(value) =>
                      setFormData({ ...formData, category: value })
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category} value={category}>
                          {category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="location">Lokasi *</Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) =>
                    setFormData({ ...formData, location: e.target.value })
                  }
                  placeholder="Alamat lengkap lokasi wisata"
                />
              </div>

              <div>
                <Label htmlFor="description">Deskripsi *</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) =>
                    setFormData({ ...formData, description: e.target.value })
                  }
                  placeholder="Deskripsi lengkap tentang tempat wisata"
                  rows={4}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="facilities">Fasilitas</Label>
                  <Textarea
                    id="facilities"
                    value={formData.facilities}
                    onChange={(e) =>
                      setFormData({ ...formData, facilities: e.target.value })
                    }
                    placeholder="Fasilitas yang tersedia"
                    rows={3}
                  />
                </div>
                <div>
                  <Label htmlFor="opening_hours">Jam Buka</Label>
                  <Input
                    id="opening_hours"
                    value={formData.opening_hours}
                    onChange={(e) =>
                      setFormData({
                        ...formData,
                        opening_hours: e.target.value,
                      })
                    }
                    placeholder="Contoh: 08:00 - 17:00"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="ticket_price">Harga Tiket</Label>
                  <Input
                    id="ticket_price"
                    value={formData.ticket_price}
                    onChange={(e) =>
                      setFormData({ ...formData, ticket_price: e.target.value })
                    }
                    placeholder="Contoh: Rp 10.000 atau Gratis"
                  />
                </div>
                <div>
                  <Label htmlFor="contact_info">Kontak</Label>
                  <Input
                    id="contact_info"
                    value={formData.contact_info}
                    onChange={(e) =>
                      setFormData({ ...formData, contact_info: e.target.value })
                    }
                    placeholder="Nomor telepon atau email"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="is_featured"
                  checked={formData.is_featured}
                  onCheckedChange={(checked) =>
                    setFormData({ ...formData, is_featured: checked })
                  }
                />
                <Label htmlFor="is_featured">Jadikan wisata unggulan</Label>
              </div>

              <div>
                <Label>Gambar Wisata *</Label>
                <ImageUpload
                  onImageUploaded={handleImageUploaded}
                  currentImageUrl={formData.image_url}
                  disabled={saving}
                  folder="tourism"
                />
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setDialogOpen(false)}
                  disabled={saving}
                >
                  Batal
                </Button>
                <Button onClick={handleSave} disabled={saving}>
                  {saving ? "Menyimpan..." : "Simpan"}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {tourismList.map((tourism) => (
          <Card key={tourism.id} className="overflow-hidden">
            <div className="relative">
              <img
                src={tourism.image_url}
                alt={tourism.name}
                className="w-full h-48 object-cover"
              />
              {tourism.is_featured && (
                <div className="absolute top-2 right-2">
                  <div className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs flex items-center">
                    <Star className="w-3 h-3 mr-1" />
                    Unggulan
                  </div>
                </div>
              )}
            </div>
            <CardHeader className="pb-2">
              <div className="flex justify-between items-start">
                <div>
                  <CardTitle className="text-lg">{tourism.name}</CardTitle>
                  <p className="text-sm text-gray-600 flex items-center mt-1">
                    <MapPin className="w-3 h-3 mr-1" />
                    {tourism.location}
                  </p>
                </div>
                <div className="flex space-x-1">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => openDialog(tourism)}
                  >
                    <Edit2 className="w-3 h-3" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(tourism.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="w-3 h-3" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <p className="text-sm text-gray-700 mb-3 line-clamp-3">
                {tourism.description}
              </p>
              <div className="space-y-2 text-xs text-gray-600">
                <div className="flex items-center">
                  <span className="font-medium w-16">Kategori:</span>
                  <span>{tourism.category}</span>
                </div>
                {tourism.opening_hours && (
                  <div className="flex items-center">
                    <Clock className="w-3 h-3 mr-1" />
                    <span className="font-medium w-15">Jam Buka:</span>
                    <span>{tourism.opening_hours}</span>
                  </div>
                )}
                {tourism.ticket_price && (
                  <div className="flex items-center">
                    <span className="font-medium w-16">Harga:</span>
                    <span>{tourism.ticket_price}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {tourismList.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">Belum ada data wisata</p>
            <p className="text-sm text-gray-500">
              Klik tombol "Tambah Wisata" untuk menambah data wisata baru
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
