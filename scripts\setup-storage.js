const { createClient } = require("@supabase/supabase-js");
const fs = require("fs");
const path = require("path");

// Load environment variables manually
const envPath = path.join(__dirname, "..", ".env");
const envContent = fs.readFileSync(envPath, "utf8");
const envVars = {};

envContent.split("\n").forEach((line) => {
  const [key, value] = line.split("=");
  if (key && value) {
    envVars[key.trim()] = value.trim();
  }
});

const supabaseUrl = envVars.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey =
  envVars.SUPABASE_SERVICE_ROLE_KEY || envVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables");
  console.log(
    "Make sure you have NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in your .env file"
  );
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function setupStorage() {
  console.log("🚀 Setting up storage for gallery feature...");
  console.log("Bucket name: images");
  console.log("Folder structure: imageFolder/gallery/");

  try {
    // Step 1: Check if bucket exists
    console.log("\n1. Checking if 'images' bucket exists...");
    const { data: buckets, error: bucketsError } =
      await supabase.storage.listBuckets();

    if (bucketsError) {
      console.error("❌ Error accessing storage:", bucketsError.message);
      return;
    }

    const imagesBucket = buckets.find((bucket) => bucket.name === "images");

    if (imagesBucket) {
      console.log("✅ 'images' bucket already exists");
    } else {
      console.log("📝 Creating 'images' bucket...");

      // Step 2: Create bucket
      const { data: createData, error: createError } =
        await supabase.storage.createBucket("images", {
          public: true,
          allowedMimeTypes: [
            "image/png",
            "image/jpeg",
            "image/gif",
            "image/webp",
          ],
          fileSizeLimit: 5242880, // 5MB
        });

      if (createError) {
        console.error("❌ Error creating bucket:", createError.message);
        console.log("\n📋 Manual Setup Required:");
        console.log(
          "Please create an 'images' bucket manually in your Supabase dashboard:"
        );
        console.log("1. Go to Storage section");
        console.log("2. Click 'New bucket'");
        console.log("3. Name: 'images'");
        console.log("4. Make it Public");
        console.log("5. Set file size limit to 5MB");
        console.log("6. Allow image MIME types");
        return;
      } else {
        console.log("✅ Successfully created 'images' bucket");
      }
    }

    // Step 3: Test folder creation by uploading dummy files
    console.log("\n2. Setting up folder structure...");

    const dummyFile = Buffer.from(
      "This is a test file to create folder structure"
    );
    const folders = [
      "imageFolder/gallery/.gitkeep",
      "imageFolder/tourism/.gitkeep",
    ];

    for (const testPath of folders) {
      const { error: uploadError } = await supabase.storage
        .from("images")
        .upload(testPath, dummyFile, {
          contentType: "text/plain",
        });

      if (uploadError) {
        console.error(
          `❌ Error creating folder structure for ${testPath}:`,
          uploadError.message
        );
      } else {
        const folderName = testPath.split("/")[1];
        console.log(
          `✅ Successfully created folder structure: imageFolder/${folderName}/`
        );

        // Clean up the test file
        await supabase.storage.from("images").remove([testPath]);
      }
    }

    console.log("\n🎉 Storage setup completed!");
    console.log("You can now upload images through the gallery admin panel.");
  } catch (error) {
    console.error("❌ Unexpected error during storage setup:", error);
  }
}

// Run the setup
if (require.main === module) {
  setupStorage();
}

module.exports = { setupStorage };
